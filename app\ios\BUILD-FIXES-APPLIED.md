# CCALC iOS Build Guide

This guide provides step-by-step instructions for building the CCALC iOS app using Xcode.

## ⚠️ CRITICAL BUILD FIXES APPLIED

**Recent Issues Resolved:**
- ✅ Fixed `NitroModules` dependency error by replacing `react-native-audio-recorder-player` with `expo-av`
- ✅ Updated Podfile with proper CocoaPods CDN source
- ✅ Created complete app icon set with calculator-inspired design
- ✅ Enhanced audio recording service for voice modulation compatibility

## Prerequisites

### Required Software
- **macOS** (iOS development requires macOS)
- **Xcode 15.0+** (latest stable version recommended)
- **Node.js 18+**
- **CocoaPods** (for iOS dependency management)
- **Expo CLI** (for Expo/EAS builds)

### Installation Commands
```bash
# Install Node.js (if not already installed)
# Download from https://nodejs.org or use Homebrew:
brew install node

# Install CocoaPods
sudo gem install cocoapods

# Install Expo CLI
npm install -g @expo/cli

# Install EAS CLI (for cloud builds)
npm install -g eas-cli
```

## Build Methods

### Method 1: Local Xcode Build (Development)

#### Step 1: Prepare the Project
```bash
# Navigate to the app directory
cd app

# Install dependencies (includes new expo-file-system)
npm install

# Generate iOS native code (if using Expo)
npx expo prebuild --platform ios --clean
```

#### Step 2: Install iOS Dependencies (UPDATED)
```bash
# Navigate to iOS directory
cd ios

# Update CocoaPods repos (fixes NitroModules error)
pod repo update

# Deintegrate and clean install
pod deintegrate
pod install --repo-update

# Return to app directory
cd ..
```

#### Step 3: Open in Xcode
```bash
# Open the workspace (NOT the .xcodeproj file)
open ios/CCALC.xcworkspace
```

## Audio Recording Migration

### ⚠️ BREAKING CHANGE: Audio Recording Service Updated

**Old Service (Removed):** `react-native-audio-recorder-player`
**New Service:** `CCLCAudioService` using `expo-av`

**Migration Steps:**
1. Update any imports from the old service:
   ```typescript
   // OLD (Remove)
   import AudioRecorderPlayer from 'react-native-audio-recorder-player';
   
   // NEW (Use instead)
   import CCLCAudioService from '../services/CCLCAudioService';
   ```

2. Update recording methods:
   ```typescript
   // OLD
   const audioRecorderPlayer = new AudioRecorderPlayer();
   await audioRecorderPlayer.startRecorder();
   
   // NEW
   await CCLCAudioService.startRecording();
   ```

**Features Maintained:**
- ✅ High-quality voice recording (44.1kHz, 128kbps)
- ✅ Voice modulation compatibility
- ✅ WebRTC integration support
- ✅ Background recording capability
- ✅ Volume control and seeking
- ✅ File management utilities

## Troubleshooting

### Common Issues (UPDATED)

#### 1. CocoaPods Issues (FIXED)
```bash
# Clear CocoaPods cache and fix NitroModules error
cd ios
pod repo update
pod deintegrate
pod install --repo-update
```

#### 2. Audio Recording Issues
```bash
# Verify expo-av installation
npm list expo-av

# Check audio permissions in Info.plist
# NSMicrophoneUsageDescription should be present
```

## Recent Changes Summary

**✅ Fixed Issues:**
- Removed incompatible `react-native-audio-recorder-player`
- Added `expo-av` and `expo-file-system` dependencies
- Updated Podfile with proper CDN source
- Created complete app icon set
- Implemented CCLCAudioService for audio recording

**✅ Enhanced Features:**
- Better audio quality (44.1kHz, 128kbps)
- Improved error handling and logging
- Background recording support
- Volume control and seeking capabilities
- File management utilities
