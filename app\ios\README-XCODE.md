# CCALC iOS Build Guide

This guide provides step-by-step instructions for building the CCALC iOS app using Xcode.

## Prerequisites

### Required Software
- **macOS** (iOS development requires macOS)
- **Xcode 15.0+** (latest stable version recommended)
- **Node.js 18+**
- **CocoaPods** (for iOS dependency management)
- **Expo CLI** (for Expo/EAS builds)

### Installation Commands
```bash
# Install Node.js (if not already installed)
# Download from https://nodejs.org or use Homebrew:
brew install node

# Install CocoaPods
sudo gem install cocoapods

# Install Expo CLI
npm install -g @expo/cli

# Install EAS CLI (for cloud builds)
npm install -g eas-cli
```

## Build Methods

### Method 1: Local Xcode Build (Development)

#### Step 1: Prepare the Project
```bash
# Navigate to the app directory
cd app

# Install dependencies
npm install

# Generate iOS native code (if using Expo)
npx expo prebuild --platform ios --clean
```

#### Step 2: Install iOS Dependencies
```bash
# Navigate to iOS directory
cd ios

# Install CocoaPods dependencies
pod install

# Return to app directory
cd ..
```

#### Step 3: Open in Xcode
```bash
# Open the workspace (NOT the .xcodeproj file)
open ios/CCALC.xcworkspace
```

#### Step 4: Configure Xcode Project
1. **Select your development team:**
   - Click on the CCALC project in the navigator
   - Select the CCALC target
   - Go to "Signing & Capabilities"
   - Select your Apple Developer Team

2. **Configure Bundle Identifier:**
   - Change bundle identifier if needed (default: `com.ccalc.app`)
   - Ensure it's unique for your Apple Developer account

3. **Select Target Device:**
   - Choose a connected iOS device or simulator
   - iOS 13.4+ required (as per deployment target)

#### Step 5: Build and Run
1. **Build the project:**
   - Press `Cmd + B` or Product → Build
   - Wait for build to complete (first build may take several minutes)

2. **Run on device/simulator:**
   - Press `Cmd + R` or Product → Run
   - App should launch on selected device/simulator

### Method 2: Expo EAS Cloud Build (Recommended for Distribution)

#### Step 1: Setup EAS
```bash
# Login to Expo account
eas login

# Initialize EAS in project (if not already done)
eas build:configure
```

#### Step 2: Build for iOS
```bash
# Preview build (for testing)
npm run build:ios:preview

# Production build (for App Store)
npm run build:ios

# Development build (with dev client)
npm run build:development
```

#### Step 3: Download and Install
- EAS will provide download links once build completes
- Install on device via TestFlight or direct installation

## Project Structure

```
app/ios/
├── CCALC.xcworkspace          # Main workspace file (OPEN THIS)
├── CCALC.xcodeproj/           # Xcode project
├── CCALC/                     # iOS app source
│   ├── AppDelegate.h/mm       # App delegate
│   ├── Info.plist            # App configuration
│   ├── CCLCVoice.h/m         # Voice processing module
│   └── Images.xcassets/      # App icons and images
├── Podfile                   # CocoaPods dependencies
└── Pods/                     # Installed dependencies
```

## Configuration Details

### Bundle Identifier
- **Default:** `com.ccalc.app`
- **Configurable in:** `app.config.js` and Xcode project settings

### iOS Deployment Target
- **Minimum:** iOS 13.4
- **Recommended:** iOS 15.0+

### Permissions Required
- **Microphone:** For voice calls and recording
- **Camera:** For photo capture and sharing
- **Photo Library:** For image selection

### Voice Processing
- **Native Module:** CCLCVoice (stub implementation included)
- **Fallback Mode:** Available for development builds
- **WebRTC:** Integrated for real-time voice calls

## Troubleshooting

### Common Issues

#### 1. CocoaPods Issues
```bash
# Clear CocoaPods cache
cd ios
pod deintegrate
pod install --repo-update
```

#### 2. Build Errors
```bash
# Clean build folder
# In Xcode: Product → Clean Build Folder
# Or via command line:
cd ios
xcodebuild clean -workspace CCALC.xcworkspace -scheme CCALC
```

#### 3. Signing Issues
- Ensure you have a valid Apple Developer account
- Check that bundle identifier is unique
- Verify provisioning profiles are up to date

#### 4. Metro/React Native Issues
```bash
# Reset Metro cache
npx react-native start --reset-cache

# Or for Expo:
npx expo start --clear
```

### Verification
Run the verification script to check project status:
```bash
npm run verify-ios
```

## Voice Modulation Features

### Development Mode
- Voice processing uses fallback/stub implementation
- All voice call functionality is simulated
- Suitable for UI/UX testing

### Production Mode
- Full voice morphing capabilities
- Real-time voice transformation
- Hidden call recording for admin panel

## Build Profiles

### Development
- **Purpose:** Local testing and development
- **Features:** Debug symbols, fast refresh, development server
- **Command:** `npm run build:development`

### Preview
- **Purpose:** Internal testing and QA
- **Features:** Production-like build, TestFlight distribution
- **Command:** `npm run build:ios:preview`

### Production
- **Purpose:** App Store submission
- **Features:** Optimized build, release configuration
- **Command:** `npm run build:ios`

## Next Steps

1. **Test the app thoroughly** on physical devices
2. **Configure push notifications** if needed
3. **Set up App Store Connect** for distribution
4. **Submit for App Store review** when ready

## Support

For build issues or questions:
1. Check the verification script output: `npm run verify-ios`
2. Review Xcode build logs for specific errors
3. Ensure all prerequisites are properly installed
4. Verify that the backend server is accessible for testing